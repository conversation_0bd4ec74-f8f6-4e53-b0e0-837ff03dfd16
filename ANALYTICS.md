# Google Cloud & Analytics Setup
Need to authorize your application to access Google Analytics data.

### Create a Google Cloud Project: 
- Go to the Google Cloud Console and create a new project (or use an existing one).
### Enable the API 
- In project, navigate to "APIs & Services" > "Library". Search for and enable the **"Google Analytics Data API".**
### Create a Service Account:
- Go to "APIs & Services" > "Credentials".
- Click "Create Credentials" and select "Service Account".
- Give it a name (e.g., analytics-viewer) and click "Create and Continue", then "Done".
### Generate a JSON Key:
- On the Credentials page, find new service account.
- Go to the "Keys" tab.
- Click "Add Key" > "Create new key", choose <PERSON><PERSON><PERSON> as the key type, and click "Create". A JSON file containing credentials will be downloaded. Keep this file secure.
### Grant Access in Google Analytics:
- Open Google Analytics dashboard.
- Go to "Admin" (bottom-left gear icon).
- In the "Property" column, select "Property Access Management".
- Click the blue "+" button in the top right and select "Add users".
- In the "Email address" field, paste the client_email from the JSON file.
- Assign the "Viewer" role. Do not check "Notify users by email".
### Find your Property ID
- In the Google Analytics "Admin" section, under the "Property" column, go to "Property Settings". "Property ID" will be displayed there. 


## Store Credential
- `EDITOR="code --wait" rails credentials:edit`
- Add the following to the credentials file:
```google_analytics:
  property_id: "YOUR_GA4_PROPERTY_ID"
  credentials: |-
    ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************```