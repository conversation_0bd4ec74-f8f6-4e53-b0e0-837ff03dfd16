# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).
#
# Example:
#
#   ["Action", "Comedy", "Drama", "Horror"].each do |genre_name|
#     MovieGenre.find_or_create_by!(name: genre_name)
#   end

puts "Email: #{Rails.application.credentials.dig(:admin, :email).inspect}"

admin_email = Rails.application.credentials.dig(:admin, :email)
admin_password = Rails.application.credentials.dig(:admin, :password)

if admin_email.present? && admin_password.present?
  unless User.exists?(email: admin_email)
    User.create!(
      email: admin_email,
      password: admin_password,
      password_confirmation: admin_password,
      role: "admin"
    )
    puts "Admin user created"
  else
    puts "Admin user already exists"
  end
else
  puts "Admin Credentials not found in credentials file"
end

puts "Seeding Categories..."

unless Category.exists?(name: "Uncategorized")
  Category.create!(name: "Uncategorized")
  puts "Created 'Uncategorized' category"
else
  puts "'Uncategorized' category already exists"
end

puts "Done Seeding!"
