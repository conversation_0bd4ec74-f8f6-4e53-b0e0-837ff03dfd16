import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static values = {
    light: String,
    dark: String,
    url: String, // URL to PATCH theme settings
    param: String, // e.g., 'light_theme' or 'dark_theme'
    storageKey: { type: String, default: "theme" }
  }
  static targets = ["select"]

  connect() {
    this.updateCheckboxState()

    // If a select dropdown is a target, set its initial value
    // based on the current theme. This is for the dashboard settings page.
    if (this.hasSelectTarget) {
      const currentTheme = document.documentElement.getAttribute("data-theme")
      this.selectTarget.value = currentTheme
    }
  }

  /**
   * Toggles between the light and dark themes.
   * Used for the simple toggle on the public site.
   */
  toggle() {
    const currentTheme = document.documentElement.getAttribute('data-theme')
    const newTheme = currentTheme === this.darkValue ? this.lightValue : this.darkValue
    this.setTheme(newTheme)
    // Persist theme to localStorage using the specified key
    localStorage.setItem(this.storageKeyValue, newTheme)
  }

  /**
   * Changes the theme from a <select> dropdown and saves it to the server.
   * This is intended for the user dashboard.
   */
  change(event) {
    const newTheme = event.target.value
    this.setTheme(newTheme)
    this.saveTheme(newTheme)
  }

  /**
   * Sets the data-theme attribute on the <html> element.
   * @param {string} themeName
   */
  setTheme(themeName) {
    document.documentElement.setAttribute("data-theme", themeName)
  }

  /**
   * Saves the selected theme to the server via a PATCH request.
   * @param {string} themeName
   */
  async saveTheme(themeName) {
    // Only proceed if the URL and param values are provided
    if (!this.hasUrlValue || !this.hasParamValue) return

    const csrfToken = document.querySelector("meta[name='csrf-token']").content

    try {
      await fetch(this.urlValue, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": csrfToken,
          "Accept": "application/json",
        },
        body: JSON.stringify({ setting: { [this.paramValue]: themeName } }),
      })
    } catch (error) {
      console.error("Error saving theme:", error)
    }
  }

  /**
   * Updates the state of a toggle checkbox based on the current theme.
   */
  updateCheckboxState() {
    const checkbox = this.element.querySelector("input[type=checkbox]")
    if (checkbox) {
      const currentTheme = document.documentElement.getAttribute('data-theme')
      checkbox.checked = (currentTheme === this.darkValue)
    }
  }
}
