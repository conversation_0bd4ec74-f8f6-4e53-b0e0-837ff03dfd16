import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static values = {
    enUrl: String,
    idUrl: String,
    current: String,
  }
  static targets = ["checkbox"]

  connect() {    
    this.checkboxTarget.checked = this.currentValue === "id"
  }

  switch(event) {    
    const destinationUrl = event.target.checked ? this.idUrlValue : this.enUrlValue    
    window.location.href = destinationUrl
  }
}
