import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["progressBar"]
  static values = {
    hideAfter: { type: Number, default: 5000 },
    transitionDuration: { type: Number, default: 300 }
  }

  connect() {
    requestAnimationFrame(() => {
      this.element.classList.remove("opacity-0", "translate-x-4")
    });

    if (this.hideAfterValue > 0) {
      this.startTime = Date.now()
      this.hideTimeout = setTimeout(() => {
        this.close()
      }, this.hideAfterValue)
      this.animationFrame = requestAnimationFrame(() => this.updateProgress())
    }
  }

  disconnect() {
    clearTimeout(this.hideTimeout)
    clearTimeout(this.removeTimeout)
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame)
    }
  }

  close() {
    clearTimeout(this.hideTimeout)
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame)
    }

    this.element.classList.add("opacity-0", "translate-x-4")

    this.removeTimeout = setTimeout(() => {
      this.element.remove()
    }, this.transitionDurationValue)
  }

  updateProgress() {
    const elapsedTime = Date.now() - this.startTime
    const progress = ((this.hideAfterValue - elapsedTime) / this.hideAfterValue) * 100

    if (this.hasProgressBarTarget) {
      this.progressBarTarget.value = Math.max(0, progress)
    }

    if (progress > 0) {
      this.animationFrame = requestAnimationFrame(() => this.updateProgress())
    }
  }
}
