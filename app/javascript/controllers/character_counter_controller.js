import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [ "input", "output" ]
  static values = {
    maxLength: Number
  }

  connect() {
    this.count()
  }

  count() {
    const currentLength = this.inputTarget.value.length
    let outputText = `${currentLength}`

    if (this.hasMaxLengthValue) {
      outputText += ` / ${this.maxLengthValue}`
    }

    this.outputTarget.textContent = outputText

    if (this.hasMaxLengthValue && currentLength > this.maxLengthValue) {
      this.outputTarget.classList.add("text-error")
      this.inputTarget.classList.add("input-error")
    } else {
      this.outputTarget.classList.remove("text-error")
      this.inputTarget.classList.remove("input-error")
    }
  }
}
