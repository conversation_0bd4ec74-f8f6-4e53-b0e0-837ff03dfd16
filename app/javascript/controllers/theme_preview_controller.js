import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="theme-preview"
export default class extends Controller {
  static targets = [ "lightSelect", "darkSelect", "lightPreview", "darkPreview", "lightPreviewName", "darkPreviewName" ]

  connect() {
    this.updateLight()
    this.updateDark()
  }

  update() {
    this.updateLight()
    this.updateDark()
  }

  updateLight() {
    const theme = this.lightSelectTarget.value
    if (theme) {
      this.lightPreviewTarget.dataset.theme = theme
      this.lightPreviewNameTarget.textContent = theme
    }
  }

  updateDark() {
    const theme = this.darkSelectTarget.value
    if (theme) {
      this.darkPreviewTarget.dataset.theme = theme
      this.darkPreviewNameTarget.textContent = theme
    }
  }
}
