import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="image-preview"
export default class extends Controller {
  static targets = [ "preview" ]

  show(event) {
    const input = event.target
    if (input.files && input.files[0]) {
      const file = input.files[0]

      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file.')
        input.value = ''
        return
      }

      // Validate file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        alert('File size must be less than 5MB.')
        input.value = ''
        return
      }

      const reader = new FileReader()

      reader.onload = (e) => {
        this.previewTarget.src = e.target.result
      }

      reader.onerror = () => {
        alert('Error reading file.')
        input.value = ''
      }

      reader.readAsDataURL(file)
    }
  }
}