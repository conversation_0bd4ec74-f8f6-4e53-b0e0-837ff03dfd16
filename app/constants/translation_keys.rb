# frozen_string_literal: true

module TranslationKeys
  # Common action keys
  module Actions
    ADD = "common.actions.add"
    SHOW = "common.actions.show"
    EDIT = "common.actions.edit"
    DELETE = "common.actions.delete"
    SAVE = "common.actions.save"
    CANCEL = "common.actions.cancel"
    BACK = "common.actions.back"
    REFRESH = "common.actions.refresh"
  end

  # Confirmation keys
  module Confirmations
    ARE_YOU_SURE = "common.confirmations.are_you_sure"
    DELETE_CONFIRMATION = "common.confirmations.delete_confirmation"
  end

  # Response keys
  module Responses
    YES = "common.responses.yes"
    NO = "common.responses.no"
  end

  # Status keys
  module Status
    PUBLISHED = "common.status.published"
    DRAFT = "common.status.draft"
    ACTIVE = "common.status.active"
    INACTIVE = "common.status.inactive"
  end

  # Common field keys
  module Fields
    NAME = "common.fields.name"
    TITLE = "common.fields.title"
    SLUG = "common.fields.slug"
    EMAIL = "common.fields.email"
    ROLE = "common.fields.role"
    AUTHOR = "common.fields.author"
    CREATED_AT = "common.fields.created_at"
    UPDATED_AT = "common.fields.updated_at"
  end

  # Page-specific keys
  module Pages
    module Home
      HEADING = "pages.home.index.heading"
      WELCOME_MESSAGE = "pages.home.index.welcome_message"
    end

    module Dashboard
      TITLE = "pages.dashboard.index.title"
      PAGEVIEWS = "pages.dashboard.index.pageviews"
      TOP_PAGES = "pages.dashboard.index.top_pages"
      PAGE_PATH = "pages.dashboard.index.page_path"
    end

    module Settings
      TITLE = "pages.settings.index.title"
      SITE_NAME = "pages.settings.index.site_name"
      SITE_DESCRIPTION = "pages.settings.index.site_description"
      SITE_LIGHT_THEME = "pages.settings.index.site_light_theme"
      SITE_DARK_THEME = "pages.settings.index.site_dark_theme"
      LIGHT_THEME = "pages.settings.index.light_theme"
      DARK_THEME = "pages.settings.index.dark_theme"
      USER_SETTINGS = "pages.settings.index.user_settings"
    end
  end

  # Resource-specific keys
  module Resources
    module Posts
      HEADING = "resources.posts.index.heading"
      NO_POSTS = "resources.posts.index.no_posts"
      NEW_HEADING = "resources.posts.new.heading"
      EDIT_HEADING = "resources.posts.edit.heading"
      SHOW_TITLE = "resources.posts.show.title"
      LIST = "resources.posts.list"
    end

    module Categories
      HEADING = "resources.categories.index.heading"
      NO_CATEGORIES = "resources.categories.index.no_categories"
      NEW_HEADING = "resources.categories.new.heading"
      EDIT_HEADING = "resources.categories.edit.heading"
      LIST = "resources.categories.list"
    end

    module Users
      HEADING = "resources.users.index.heading"
      NO_USERS = "resources.users.index.no_users"
      NEW_HEADING = "resources.users.new.heading"
      EDIT_HEADING = "resources.users.edit.heading"
      LIST = "resources.users.list"
    end
  end

  # Flash message keys
  module Flash
    module Notices
      USER_CREATED = "flash.notices.user_created"
      USER_UPDATED = "flash.notices.user_updated"
      POST_CREATED = "flash.notices.post_created"
      POST_UPDATED = "flash.notices.post_updated"
      CATEGORY_CREATED = "flash.notices.category_created"
      CATEGORY_UPDATED = "flash.notices.category_updated"
    end

    module Errors
      OOPS = "flash.errors.oops"
      USER_NOT_CREATED = "flash.errors.user_not_created"
      USER_NOT_UPDATED = "flash.errors.user_not_updated"
      POST_NOT_CREATED = "flash.errors.post_not_created"
      POST_NOT_UPDATED = "flash.errors.post_not_updated"
      CATEGORY_NOT_CREATED = "flash.errors.category_not_created"
      CATEGORY_NOT_UPDATED = "flash.errors.category_not_updated"
    end

    module Warnings
      MAX_DESC = "flash.warnings.max_desc"
    end
  end

  # Authorization keys
  module Authorization
    ACCESS_DENIED_MESSAGE = "authorization.access_denied.message"
    ACCESS_DENIED_TITLE = "authorization.access_denied.title"
  end

  # Navigation keys
  module Navigation
    VIEW_SITE = "navigation.sidebar.view_site"
  end

  # ActiveRecord keys
  module ActiveRecord
    module Models
      POST = "activerecord.models.post"
      CATEGORY = "activerecord.models.category"
      USER = "activerecord.models.user"
    end

    module Attributes
      module Post
        TITLE = "activerecord.attributes.post.title"
        EXCERPT = "activerecord.attributes.post.excerpt"
        BODY = "activerecord.attributes.post.body"
        CATEGORY = "activerecord.attributes.post.category"
        PUBLISHED = "activerecord.attributes.post.published"
        PUBLISHED_AT = "activerecord.attributes.post.published_at"
        FEATURED_IMAGE = "activerecord.attributes.post.featured_image"
        SEO_TITLE = "activerecord.attributes.post.seo_title"
      end

      module Category
        NAME = "activerecord.attributes.category.name"
        SLUG = "activerecord.attributes.category.slug"
        DESCRIPTION = "activerecord.attributes.category.description"
      end

      module User
        EMAIL = "activerecord.attributes.user.email"
        PASSWORD = "activerecord.attributes.user.password"
        PASSWORD_CONFIRMATION = "activerecord.attributes.user.password_confirmation"
        ROLE = "activerecord.attributes.user.role"
        AVATAR = "activerecord.attributes.user.avatar"
      end
    end
  end

  # Helper methods for dynamic key generation
  class << self
    def resource_heading(resource, action)
      "resources.#{resource.to_s.pluralize}.#{action}.heading"
    end

    def resource_title(resource, action)
      "resources.#{resource.to_s.pluralize}.#{action}.title"
    end

    def page_title(page, section = :index)
      "pages.#{page}.#{section}.title"
    end

    def flash_notice(action, resource = nil)
      if resource
        "flash.notices.#{resource}_#{action}"
      else
        "flash.notices.#{action}"
      end
    end

    def flash_error(action, resource = nil)
      if resource
        "flash.errors.#{resource}_#{action}"
      else
        "flash.errors.#{action}"
      end
    end

    def model_attribute(model, attribute)
      model_name = model.is_a?(Class) ? model.model_name.i18n_key : model.class.model_name.i18n_key
      "activerecord.attributes.#{model_name}.#{attribute}"
    end

    def model_name(model)
      model_name = model.is_a?(Class) ? model.model_name.i18n_key : model.class.model_name.i18n_key
      "activerecord.models.#{model_name}"
    end
  end
end
