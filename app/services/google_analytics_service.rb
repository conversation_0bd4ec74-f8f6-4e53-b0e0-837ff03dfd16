# frozen_string_literal: true

require "google/analytics/data/v1beta"

class GoogleAnalyticsService
  class ApiError < StandardError; end

  def self.client
    # The credentials are provided as a string, so we need to parse them.
    creds = JSON.parse(Rails.application.credentials.google_analytics[:credentials])

    Google::Analytics::Data::V1beta::AnalyticsData::Client.new do |config|
      config.credentials = creds
    end
  end

  def self.pageviews_for_range(start_date:, end_date:)
    begin
      property_id = Rails.application.credentials.google_analytics[:property_id]

      request = Google::Analytics::Data::V1beta::RunReportRequest.new(
        property: "properties/#{property_id}",
        date_ranges: [
          Google::Analytics::Data::V1beta::DateRange.new(
            start_date: start_date,
            end_date: end_date
          )
        ],
        dimensions: [
          Google::Analytics::Data::V1beta::Dimension.new(name: "date")
        ],
        metrics: [
          Google::Analytics::Data::V1beta::Metric.new(name: "screenPageViews")
        ],
        order_bys: [
          Google::Analytics::Data::V1beta::OrderBy.new(
            dimension: Google::Analytics::Data::V1beta::OrderBy::DimensionOrderBy.new(
              dimension_name: "date"
            )
          )
        ]
      )

      client.run_report(request).rows
    rescue StandardError => e
      Rails.logger.error "Google Analytics Service Error: #{e.class} - #{e.message}"
      raise ApiError, "Failed to fetch data from Google Analytics. Please check credentials and API configuration."
    end
  end

  def self.top_pages_for_range(start_date:, end_date:, limit: 10)
    begin
      property_id = Rails.application.credentials.google_analytics[:property_id]

      request = Google::Analytics::Data::V1beta::RunReportRequest.new(
        property: "properties/#{property_id}",
        date_ranges: [
          Google::Analytics::Data::V1beta::DateRange.new(start_date: start_date, end_date: end_date)
        ],
        dimensions: [
          Google::Analytics::Data::V1beta::Dimension.new(name: "pagePath")
        ],
        metrics: [
          Google::Analytics::Data::V1beta::Metric.new(name: "screenPageViews"),
          Google::Analytics::Data::V1beta::Metric.new(name: "activeUsers")
        ],
        order_bys: [
          Google::Analytics::Data::V1beta::OrderBy.new(
            metric: Google::Analytics::Data::V1beta::OrderBy::MetricOrderBy.new(metric_name: "screenPageViews"),
            desc: true
          )
        ],
        limit: limit
      )
      client.run_report(request).rows
    rescue StandardError => e
      Rails.logger.error "Google Analytics Service Error (top_pages): #{e.class} - #{e.message}"
      raise ApiError, "Failed to fetch top pages data from Google Analytics."
    end
  end
end
