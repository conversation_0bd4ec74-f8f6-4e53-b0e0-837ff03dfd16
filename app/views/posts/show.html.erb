<% content_for :title, "Showing post" %>

<div class="md:w-2/3 w-full">  

  <h1 class="font-bold text-4xl">Showing post</h1>
  <% if @post.featured_image.attached? %>
    <%= image_tag(
        @post.featured_image.variant(resize_to_limit: [800, 400]),
        alt: @post.excerpt.presence || @post.title
      ) %>
  <% end %>
  <div class="prose">
    <%= @post.body.html_safe %>
  </div>
  <%= render @post %>

  <% if user_signed_in? && can?(:manage, Post) %>
    <%= link_to "Edit this post", edit_dashboard_post_path(@post), class: "w-full sm:w-auto text-center rounded-md px-3.5 py-2.5 bg-gray-100 hover:bg-gray-50 inline-block font-medium" %>
    <%= button_to "Destroy this post", dashboard_post_path(@post), method: :delete, form_class: "sm:inline-block mt-2 sm:mt-0 sm:ml-2", class: "w-full rounded-md px-3.5 py-2.5 text-white bg-red-600 hover:bg-red-500 font-medium cursor-pointer", data: { turbo_confirm: "Are you sure?" } %>
  <% end %>
  <%= link_to "Back to posts", posts_path, class: "w-full sm:w-auto text-center mt-2 sm:mt-0 sm:ml-2 rounded-md px-3.5 py-2.5 bg-gray-100 hover:bg-gray-50 inline-block font-medium" %>
</div>
<script type="application/ld+json">
  <%= raw(post_json_ld(@post).to_json) %>
</script>