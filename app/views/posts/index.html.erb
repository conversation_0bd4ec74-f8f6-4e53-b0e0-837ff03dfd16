<% content_for :title, "Posts" %>

<div class="w-full">
  <% if notice.present? %>
    <p class="py-2 px-3 bg-green-50 mb-5 text-green-500 font-medium rounded-md inline-block" id="notice"><%= notice %></p>
  <% end %>

  <div class="flex justify-between items-center">
    <h1 class="font-bold text-4xl">Posts</h1>    
  </div>

  <div id="posts" class="min-w-full divide-y divide-gray-200 space-y-5">
    <% if @posts.any? %>
      <% @posts.each do |post| %>
        <div class="flex flex-col sm:flex-row justify-between items-center pb-5 sm:pb-0">
          <% if post.featured_image.attached? %>
            <%= image_tag post.featured_image.variant(resize_to_limit: [400, 200]) %>
          <% end %>
          <%= render post %>
          <div class="w-full sm:w-auto flex flex-col sm:flex-row space-x-2 space-y-2">
            <%= link_to "Show", post, class: "w-full sm:w-auto text-center rounded-md px-3.5 py-2.5 bg-gray-100 hover:bg-gray-50 inline-block font-medium" %>
            <%= link_to "Edit", edit_dashboard_post_path(post), class: "w-full sm:w-auto text-center rounded-md px-3.5 py-2.5 bg-gray-100 hover:bg-gray-50 inline-block font-medium" %>
            <%= button_to "Destroy", post, method: :delete, class: "w-full sm:w-auto rounded-md px-3.5 py-2.5 text-white bg-red-600 hover:bg-red-500 font-medium cursor-pointer", data: { turbo_confirm: "Are you sure?" } %>
          </div>
        </div>
      <% end %>
    <% else %>
      <p class="text-center my-10">No posts found.</p>
    <% end %>
  </div>
</div>
