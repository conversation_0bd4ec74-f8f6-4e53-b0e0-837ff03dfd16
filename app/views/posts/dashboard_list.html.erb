<div class="overflow-x-auto">
    <%= link_to t(TranslationKeys::Resources::Posts::NEW_HEADING), new_dashboard_post_path, class: "btn btn-outline btn-primary" %>
    <table class="table">
        <thead>
            <tr>
                <th><%= t(TranslationKeys::ActiveRecord::Attributes::Post::TITLE) %></th>
                <th><%= t(TranslationKeys::ActiveRecord::Attributes::Post::EXCERPT) %></th>
                <th><%= t(TranslationKeys::ActiveRecord::Attributes::Post::CATEGORY) %></th>
                <th><%= t(TranslationKeys::ActiveRecord::Attributes::Post::PUBLISHED) %></th>
                <th><%= t(TranslationKeys::ActiveRecord::Attributes::Post::PUBLISHED_AT) %></th>
                <th><%= translate_field(:author) %></th>
                <th><%= translate_field(:actions) %></th>
        </thead>
        <tbody>
            <% @posts.each do |post| %>
                <tr>
                    <td>
                        <% if post.title.length > 20 %>
                            <span class="tooltip tooltip-primary tooltip-right" data-tip="<%= post.title %>"><%= truncate(post.title, length: 20) %></span>
                        <% else %>
                            <%= post.title %>
                        <% end %>
                    </td>
                    <td><%= post.excerpt %></td>
                    <td><%= post.category.name %></td>
                    <td><%= translate_boolean(post.published?) %></td>
                    <td><%= localize_datetime(post.published_at) %></td>
                    <td><%= post.user.email %></td>
                    <td class="flex gap-2">
                        <%= link_to translate_action(:show), post, class: "btn btn-primary" %>
                        <%= link_to translate_action(:edit), edit_dashboard_post_path(post), class: "btn btn-secondary" %>
                        <%= button_to translate_action(:delete), post, method: :delete, class: "btn btn-error", data: { turbo_confirm: translate_confirmation(:delete, item: translate_model(Post)) } %>
                    </td>
                </tr>
            <% end %>
        </tbody>
    </table>
</div>