<div class="overflow-x-auto">
    <%= link_to t('posts.new.heading', model: Post.model_name.human), new_dashboard_post_path, class: "btn btn-outline btn-primary" %>
    <table class="table">
        <thead>
            <tr>
                <th><%= t('activerecord.attributes.post.title') %></th>
                <th><%= t('activerecord.attributes.post.excerpt') %></th>
                <th><%= t('activerecord.attributes.post.category') %></th>
                <th><%= t('activerecord.attributes.post.published') %></th>
                <th><%= t('activerecord.attributes.post.published_at') %></th>
                <th><%= t('author') %></th>
                <th><%= t('actions') %></th>
        </thead>
        <tbody>
            <% @posts.each do |post| %>
                <tr>
                    <td>
                        <% if post.title.length > 20 %>
                            <span class="tooltip tooltip-primary tooltip-right" data-tip="<%= post.title %>"><%= truncate(post.title, length: 20) %></span>
                        <% else %>
                            <%= post.title %>
                        <% end %>
                    </td>
                    <td><%= post.excerpt %></td>
                    <td><%= post.category.name %></td>
                    <td><%= post.published? ? t('yes') : t('no') %></td>
                    <td><%= post.published_at %></td>   
                    <td><%= post.user.email %></td>                                     
                    <td class="flex gap-2">
                        <%= link_to t('show'), post, class: "btn btn-primary" %>
                        <%= link_to t('edit'), edit_dashboard_post_path(post), class: "btn btn-secondary" %>
                        <%= button_to t('delete'), post, method: :delete, class: "btn btn-error", data: { turbo_confirm: t('are_you_sure') } %>
                    </td>
                </tr>
            <% end %>
        </tbody>
    </table>
</div>