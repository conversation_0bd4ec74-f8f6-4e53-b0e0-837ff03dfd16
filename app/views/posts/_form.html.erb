<%= form_with(model: post, url: post.persisted? ? dashboard_post_path(post) : dashboard_posts_path, class: "contents") do |form| %>
<% if post.errors.any? %>
<div role="alert" class="border-s-4 border-error-content bg-error p-4">
  <div class="flex items-center gap-2 text-error-content">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-5">
      <path
        fill-rule="evenodd"
        d="M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z"
        clip-rule="evenodd"
      />
    </svg>

    <strong class="font-medium"><%= pluralize(post.errors.count, "error") %> prohibited this post from being saved: </strong>
  </div>
  <ol class="list-decimal pl-4">
    <% post.errors.each do |error| %>      
        <li><%= error.full_message %></li>      
    <% end %>
  </ol>    
</div>  
<% end %>
<div class="grid grid-cols-1 gap-4 lg:grid-cols-3 lg:gap-8">
  <div class="lg:col-span-2">

  </div>
  <div>

  </div>
</div>  
  <div class="field">
    <%= form.label :featured_image %><br>
    <%= form.file_field :featured_image %>
  </div>
  <div class="my-5" data-controller="character-counter" data-character-counter-max-length-value="70">
    <div class="flex justify-between">
      <%= form.label :title %>
      <span class="text-sm" data-character-counter-target="output"></span>
    </div>
    <%= form.text_field :title, class: ["input w-full", {"input-neutral": post.errors[:title].none?, "input-error": post.errors[:title].any?}], data: { action: "input->character-counter#count", "character-counter-target": "input" } %>
  </div>

  <div class="my-5">
    <%= form.label :slug %>
    <%= form.text_field :slug, class: ["input w-full", {"input-neutral": post.errors[:title].none?, "input-error": post.errors[:title].any?}] %>
    <p class="text-sm mt-1">The slug is auto-generated from the title if left blank.</p>
  </div>

  <div class="my-5">
    <%= form.label :category %>
    <%= form.collection_select :category_id, Category.all, :id, :name, { prompt: "Select a category" }, { class: ["select w-full", {"select-neutral": post.errors[:title].none?, "select-error": post.errors[:title].any?}] } %>
  </div>

  <div class="my-5" data-controller="character-counter" data-character-counter-max-length-value="160">
    <div class="flex justify-between">
      <%= form.label :excerpt %>
      <span class="text-sm" data-character-counter-target="output"></span>
    </div>
    <%= form.textarea :excerpt, rows: 4, class: ["textarea w-full", {"textarea-neutral": post.errors[:title].none?, "textarea-error": post.errors[:title].any?}], data: { action: "input->character-counter#count", "character-counter-target": "input" } %>
    <p class="text-sm mt-1">A short summary of the post. Max 160 characters.</p>
  </div>

  <div class="my-5">
    <%= form.label :body %>
    <div
      data-controller="tiptap"
      data-tiptap-content-value="<%= @post.body.to_json %>"
    >
      <%= form.hidden_field :body, data: { "tiptap-target": "input" } %>
      <div data-tiptap-target="editor" class="border rounded-md" style="min-height: 250px;"></div>

      <div class="mt-2 flex gap-2">
        <button type="button" class="btn" data-action="click->tiptap#insertImageByUrl">Insert URL image</button>

        <label class="btn">
          Upload image
          <input type="file" accept="image/*" data-action="change->tiptap#uploadFile" data-tiptap-target="fileInput" style="display:none" />
        </label>        
        <button type="button" data-align="left" data-action="click->tiptap#setAlign">Left</button>
        <button type="button" data-align="center" data-action="click->tiptap#setAlign">Center</button>
        <button type="button" data-align="right" data-action="click->tiptap#setAlign">Right</button>
        <button type="button" data-align="justify" data-action="click->tiptap#setAlign">Justify</button>
      </div>
    </div>
  </div>

  <div class="my-5" data-controller="character-counter" data-character-counter-max-length-value="70">
    <div class="flex justify-between">
      <%= form.label :seo_title %>
      <span class="text-sm" data-character-counter-target="output"></span>
    </div>
    <%= form.text_field :seo_title, class: ["input w-full", {"input-neutral": post.errors[:title].none?, "input-error": post.errors[:title].any?}], data: { action: "input->character-counter#count", "character-counter-target": "input" } %>
    <p class="text-sm mt-1">If blank, the main title will be used. Max 70 characters.</p>
  </div>

  <div class="my-5">
    <%= form.label :meta_description %>
    <%= form.text_field :meta_description, class: ["input w-full", {"input-neutral": post.errors[:title].none?, "input-error": post.errors[:title].any?}] %>
    <p class="text-sm mt-1">If blank, the excerpt will be used.</p>
  </div>

  <div class="my-5 flex items-center gap-2">
    <%= form.label :published %>
    <%= form.checkbox :published, class: ["checkbox", {"checkbox-neutral": post.errors[:published].none?, "checkbox-error": post.errors[:published].any?}] %>
  </div>

  <div class="my-5">
    <%= form.label :published_at %>
    <%= form.datetime_field :published_at, class: ["input w-full", {"input-neutral": post.errors[:title].none?, "input-error": post.errors[:title].any?}] %>
  </div>

  <div class="inline">
    <%= form.submit nil, class: "w-full sm:w-auto rounded-md px-3.5 py-2.5 bg-blue-600 hover:bg-blue-500 text-white inline-block font-medium cursor-pointer" %>
  </div>
<% end %>
