<div class="fixed top-5 right-5 w-full max-w-xs space-y-4 z-50">
  <% flash.each do |type, message| %>
    <%
      color_suffix = case type.to_s
      when "notice"
        "info"
      when "alert"
        "error"
      when "success"
        "success"
      else
        "info"
      end
      type_class = "alert-#{color_suffix}"
      progress_class = "progress-#{color_suffix}"

      svg_path = case type.to_s
      when "notice", "success"
        "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
      when "alert"
        "M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
      end
    %>
    <div data-controller="alert" role="alert" class="relative alert <%= type_class %> shadow-lg transition-all duration-300 ease-in-out opacity-0 translate-x-4 overflow-hidden">
      <div class="flex items-start">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="h-6 w-6 shrink-0 stroke-current"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<%= svg_path %>" /></svg>
        <span class="flex-1"><%= message %></span>
      </div>
      <button data-action="click->alert#close" class="btn btn-sm btn-circle btn-ghost absolute top-1 right-1">✕</button>
      <progress data-alert-target="progressBar" class="progress progress-primary-content absolute bottom-0 left-0 h-1 w-full" value="100" max="100"></progress>
    </div>
  <% end %>  
</div>