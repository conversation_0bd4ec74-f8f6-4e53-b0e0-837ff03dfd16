<div class="grid grid-cols-2 gap-4 mt-2">
    <div class="border-base-content/20 hover:border-base-content/40 overflow-hidden rounded-lg border outline-2 outline-offset-2 outline-transparent">
        <div class="bg-base-100 text-base-content w-full cursor-pointer font-sans" data-theme-preview-target="lightPreview" data-theme="<%= light_theme %>">
            <div class="grid grid-cols-5 grid-rows-3">
                <div class="bg-base-200 col-start-1 row-span-2 row-start-1"></div> 
                <div class="bg-base-300 col-start-1 row-start-3"></div> 
                <div class="bg-base-100 col-span-4 col-start-2 row-span-3 row-start-1 flex flex-col gap-1 p-2">
                    <div class="font-bold text-left" data-theme-preview-target="lightPreviewName"><%= light_theme %></div>  
                    <div class="flex flex-wrap gap-1">
                        <div class="bg-primary flex aspect-square w-5 items-center justify-center rounded lg:w-6 tooltip tooltip-primary" data-tip="Primary">
                            <div class="text-primary-content text-sm font-bold">P</div>
                        </div> 
                        <div class="bg-secondary flex aspect-square w-5 items-center justify-center rounded lg:w-6 tooltip tooltip-secondary" data-tip="Secondary">
                            <div class="text-secondary-content text-sm font-bold">S</div>
                        </div> 
                        <div class="bg-accent flex aspect-square w-5 items-center justify-center rounded lg:w-6 tooltip tooltip-accent" data-tip="Accent">
                            <div class="text-accent-content text-sm font-bold">A</div>
                        </div> 
                        <div class="bg-neutral flex aspect-square w-5 items-center justify-center rounded lg:w-6 tooltip tooltip-neutral" data-tip="Neutral">
                            <div class="text-neutral-content text-sm font-bold">N</div>
                        </div>
                    </div>
                    <div class="flex flex-wrap gap-1">
                        <div class="bg-info flex aspect-square w-5 items-center justify-center rounded lg:w-6 tooltip tooltip-info" data-tip="Info">
                            <div class="text-info-content text-sm font-bold">I</div>
                        </div> 
                        <div class="bg-success flex aspect-square w-5 items-center justify-center rounded lg:w-6 tooltip tooltip-success" data-tip="Success">
                            <div class="text-success-content text-sm font-bold">S</div>
                        </div> 
                        <div class="bg-warning flex aspect-square w-5 items-center justify-center rounded lg:w-6 tooltip tooltip-warning" data-tip="Warning">
                            <div class="text-warning-content text-sm font-bold">W</div>
                        </div> 
                        <div class="bg-error flex aspect-square w-5 items-center justify-center rounded lg:w-6 tooltip tooltip-error" data-tip="Error">
                            <div class="text-error-content text-sm font-bold">E</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="border-base-content/20 hover:border-base-content/40 overflow-hidden rounded-lg border outline-2 outline-offset-2 outline-transparent">
        <div class="bg-base-100 text-base-content w-full cursor-pointer font-sans" data-theme-preview-target="darkPreview" data-theme="<%= dark_theme %>">
            <div class="grid grid-cols-5 grid-rows-3">
                <div class="bg-base-200 col-start-1 row-span-2 row-start-1"></div> 
                <div class="bg-base-300 col-start-1 row-start-3"></div> 
                <div class="bg-base-100 col-span-4 col-start-2 row-span-3 row-start-1 flex flex-col gap-1 p-2">
                    <div class="font-bold text-left" data-theme-preview-target="darkPreviewName"><%= dark_theme %></div> 
                    <div class="flex flex-wrap gap-1">
                        <div class="bg-primary flex aspect-square w-5 items-center justify-center rounded lg:w-6 tooltip tooltip-primary" data-tip="Primary">
                            <div class="text-primary-content text-sm font-bold">P</div>
                        </div> 
                        <div class="bg-secondary flex aspect-square w-5 items-center justify-center rounded lg:w-6 tooltip tooltip-secondary" data-tip="Secondary">
                            <div class="text-secondary-content text-sm font-bold">S</div>
                        </div> 
                        <div class="bg-accent flex aspect-square w-5 items-center justify-center rounded lg:w-6 tooltip tooltip-accent" data-tip="Accent">
                            <div class="text-accent-content text-sm font-bold">A</div>
                        </div> 
                        <div class="bg-neutral flex aspect-square w-5 items-center justify-center rounded lg:w-6 tooltip tooltip-neutral" data-tip="Neutral">
                            <div class="text-neutral-content text-sm font-bold">N</div>
                        </div>
                    </div>
                    <div class="flex flex-wrap gap-1">
                        <div class="bg-info flex aspect-square w-5 items-center justify-center rounded lg:w-6 tooltip tooltip-info" data-tip="Info">
                            <div class="text-info-content text-sm font-bold">I</div>
                        </div> 
                        <div class="bg-success flex aspect-square w-5 items-center justify-center rounded lg:w-6 tooltip tooltip-success" data-tip="Success">
                            <div class="text-success-content text-sm font-bold">S</div>
                        </div> 
                        <div class="bg-warning flex aspect-square w-5 items-center justify-center rounded lg:w-6 tooltip tooltip-warning" data-tip="Warning">
                            <div class="text-warning-content text-sm font-bold">W</div>
                        </div> 
                        <div class="bg-error flex aspect-square w-5 items-center justify-center rounded lg:w-6 tooltip tooltip-error" data-tip="Error">
                            <div class="text-error-content text-sm font-bold">E</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
