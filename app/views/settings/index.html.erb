<div class="sm:w-1/2 mx-auto">
    <div class="text-center">
                <h2 class="text-3xl font-bold text-base-content"><%= t ('settings.title') %></h2>
                <% if can?(:update, :settings) %>
                    <div data-controller="theme-preview">
                        <%= form_with scope: :setting, url: dashboard_settings_path, method: :patch, local: true do |form| %>
                            <h2 class="text-xl mt-4"><%= t('settings.site') %></h2>
                            <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-8">
                                <div class="mt-4">
                                    <%= form.label :site_name, t('settings.site_name'), class: "block text-sm font-medium mb-1 text-left"  %>
                                    <%= form.text_field :site_name, value: @site_name, class: "input input-primary w-full" %>
                                </div>
                                <div class="sm:mt-4">
                                    <%= form.label :site_description, t('settings.site_description'), class: "block text-sm font-medium mb-1 text-left"  %>
                                    <%= form.text_field :site_description, value: @site_description, class: "input input-primary w-full validator", maxlength: 160, title: t('error.max_desc')%>
                                    <p class="validator-hint"><%= t('error.max_desc') %></p>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-8">
                                <div class="mt-2">
                                    <%= form.label :site_light_theme, t('settings.light_theme'), class: "block text-sm font-medium mb-1 text-left" %>
                                    <%= form.select :site_light_theme, options_for_select(@light_theme_list, @site_light_theme), { prompt: 'Pick a light theme' }, { class: "select select-primary w-full", data: { theme_preview_target: "lightSelect", action: "change->theme-preview#update" } } %>
                                </div>
                                <div class="sm:mt-2">
                                    <%= form.label :site_dark_theme, t('settings.dark_theme'), class: "block text-sm font-medium mb-1 text-left" %>
                                    <%= form.select :site_dark_theme, options_for_select(@dark_theme_list, @site_dark_theme), { prompt: 'Pick a dark theme' }, { class: "select select-primary w-full", data: { theme_preview_target: "darkSelect", action: "change->theme-preview#update" } } %>
                                </div>
                            </div>
                            <%= render 'partials/theme_preview', light_theme: @site_light_theme, dark_theme: @site_dark_theme %>
                            <%= form.submit t('settings.save_site_settings', default: 'Save Site Settings'), class: "btn btn-primary mt-4" %>
                        <% end %>                        
                    </div>
                <% end %>
                <div data-controller="theme-preview">
                    <%= form_with scope: :setting, url: dashboard_settings_path, method: :patch, local: true do |form| %>
                        <h2 class="text-xl mt-4"><%= t ('settings.user') %></h2>
                        <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-8">
                            <div class="mt-2">
                                <%= form.label :light_theme, t('settings.light_theme'), class: "block text-sm font-medium mb-1 text-left" %>
                                <%= form.select :light_theme, options_for_select(@light_theme_list, @user_light_theme), { prompt: 'Pick a light theme' }, { class: "select select-primary w-full", data: { theme_preview_target: "lightSelect", action: "change->theme-preview#update" } } %>
                            </div>
                            <div class="sm:mt-2">
                                <%= form.label :dark_theme, t('settings.dark_theme'), class: "block text-sm font-medium mb-1 text-left" %>
                                <%= form.select :dark_theme, options_for_select(@dark_theme_list, @user_dark_theme), { prompt: 'Pick a dark theme' }, { class: "select select-primary w-full", data: { theme_preview_target: "darkSelect", action: "change->theme-preview#update" } } %>
                            </div>
                        </div>
                        <%= render 'partials/theme_preview', light_theme: @user_light_theme, dark_theme: @user_dark_theme %>
                        <%= form.submit t('settings.save_user_settings', default: 'Save User Settings'), class: "btn btn-primary mt-4" %>
                    <% end %>                    
                </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {        
            localStorage.removeItem('theme');
            localStorage.removeItem('dashboard-theme');            
            sessionStorage.setItem('themeUpdated', 'true');
        });
    }    
    if (sessionStorage.getItem('themeUpdated') === 'true') {
        sessionStorage.removeItem('themeUpdated');     
        setTimeout(function() {
            window.location.reload(true);
        }, 100);
    }
});
</script>