<div class="card bg-base-100">
    <div class="card-body overflow-x-auto">
        <div class="card-actions justify-end">
            <%= link_to t('categories.new.heading', model: Category.model_name.human), new_dashboard_category_path, class: "btn btn-outline btn-primary" %>
        </div>
        <table class="table">
            <thead>
                <tr>
                    <th><%= t('activerecord.attributes.category.name') %></th>
                    <th><%= t('activerecord.attributes.category.slug') %></th>
                    <th><%= t('actions') %></th>
            </thead>
            <tbody>
                <% @categories.each do |cat| %>
                    <tr>
                        <td><%= cat.name %></td>
                        <td><%= cat.slug %></td>
                        <td class="flex gap-2">
                            <%= link_to t('show'), cat, class: "btn btn-primary" %>
                            <%= link_to t('edit'), edit_dashboard_category_path(cat), class: "btn btn-secondary" %>
                            <%= button_to t('delete'), cat, method: :delete, class: "btn btn-error", data: { turbo_confirm: t('are_you_sure') } %>
                        </td>
                    </tr>
                <% end %>
            </tbody>
        </table>
    </div>        
</div>