<div class="overflow-x-auto">
    <% if can? :create, User %>
        <button class="btn" onclick="my_modal_1.showModal()"><%= t('add') %> <%= t('user') %></button>
    <% end %>
    <table class="table">
        <thead>
            <tr>
                <th>No</th>
                <th><%= t ('email') %></th>
                <th><%= t ('role') %></th>
                <th><%= t ('created_at') %></th>
                <% if can? :create, User %>
                  <th><%= t ('actions') %></th>
                <% end %>
            </tr>
        </thead>
        <tbody>
            <% @users.each_with_index do |user, index| %>
                <tr>
                    <td><%= @pagy.from + index %></td>
                    <td><%= user.email %></td>       
                    <td><%= user.role %></td>   
                    <td><%= user.created_at %></td>
                    <% if can? :create, User %>
                      <td>
                        <% if can? :update, user %>
                            <button class="btn" onclick="edit_modal_<%= user.id %>.showModal()"><%= t('edit') %></button>
                        <% end %>
                      </td>
                    <% end %>
                </tr>                
                <% if can? :update, user %>
                    <dialog id="edit_modal_<%= user.id %>" class="modal">
                      <div class="modal-box">
                        <h3 class="text-lg font-bold"><%= t('edit') %> <%= t('user') %></h3>
                            <%= form_with(model: user, url: dashboard_d_user_path(user), method: :patch) do |f| %>
                                <%= f.email_field :email, class: "input input-primary w-full mt-2", placeholder: "Email" %>
                                <%= f.select :role, options_for_select([["Viewer", "viewer"], ["Admin", "admin"], ["Editor", "editor"], ["Author", "author"]], user.role), { include_blank: 'Pick a role' }, { class: "select select-primary w-full mt-2" } %>
                                <p class="text-sm text-gray-500 mt-2">Leave password fields blank to keep the current password.</p>
                                <%= f.password_field :password, class: "input input-primary w-full mt-2", placeholder: "New Password" %>
                                <%= f.password_field :password_confirmation, class: "input input-primary w-full mt-2", placeholder: "Confirm New Password" %>
                                <%= f.submit t('edit'), class: "btn btn-primary mt-2" %>
                            <% end %>
                        <div class="modal-action">
                          <form method="dialog">
                            <button class="btn">Close</button>
                          </form>
                        </div>
                      </div>
                    </dialog>
                <% end %>
            <% end %>
        </tbody>
    </table>
    <%= render "partials/pagination", pagy: @pagy %>  
</div>
<% if can? :create, User %>
    <dialog id="my_modal_1" class="modal">
      <div class="modal-box">
        <h3 class="text-lg font-bold"><%= t('add') %> <%= t('user') %></h3>
            <%= form_with(model: @user, url: dashboard_users_path, method: :post) do |f| %>
                <%= f.email_field :email, class: "input input-primary w-full mt-2", placeholder: "Email" %>
                <%= f.select :role, options_for_select([["Viewer", "viewer"], ["Admin", "admin"], ["Editor", "editor"], ["Author", "author"]]), { include_blank: 'Pick a role' }, { class: "select select-primary w-full mt-2" } %>
                <%= f.password_field :password, class: "input input-primary w-full mt-2", placeholder: "Password" %>
                <%= f.password_field :password_confirmation, class: "input input-primary w-full mt-2", placeholder: "Confirm Password" %>
                <%= f.submit "Add", class: "btn btn-primary mt-2" %>
            <% end %>
        <div class="modal-action">
          <form method="dialog">                
            <button class="btn">Close</button>
          </form>
        </div>
      </div>
    </dialog>
<% end %>
