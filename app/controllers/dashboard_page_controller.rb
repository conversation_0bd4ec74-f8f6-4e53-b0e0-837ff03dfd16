class DashboardPageController < ApplicationController
  before_action :authorize_dashboard, only: [ :index ]
  before_action :authenticate_user!
  before_action :set_default_breadcrumb, except: [ :analytics_data ]
  before_action :set_user, only: [ :update_user ]
  layout "dashboard"

  def index
    set_meta_tags title: "Dashboard"
  end

  def users
    authorize! :read, User
    set_meta_tags title: "Users"
    helpers.add_breadcrumb "Users", nil
    @pagy, @users = pagy(User.order(created_at: :desc), limit: 10)
    @user = User.new
  end

  def create_user
    authorize! :create, User
    @user = User.new(user_params)
    if @user.save
      redirect_to dashboard_users_path, notice: t(TranslationKeys::Flash::Notices::USER_CREATED)
    else
      redirect_to dashboard_users_path, alert: t(TranslationKeys::Flash::Errors::USER_NOT_CREATED)
    end
  end

  def update_user
    authorize! :update, @user
    if @user.update_without_password(user_params)
      redirect_to dashboard_users_path, notice: t(TranslationKeys::Flash::Notices::USER_UPDATED)
    else
      redirect_to dashboard_users_path, alert: @user.errors.full_messages.join(", ")
    end
  end

  def analytics_data
    start_date = params.fetch(:start_date, 30.days.ago.to_date.to_s)
    end_date = params.fetch(:end_date, Date.today.to_s)

    chart_rows = GoogleAnalyticsService.pageviews_for_range(start_date: start_date, end_date: end_date) || []
    table_rows = GoogleAnalyticsService.top_pages_for_range(start_date: start_date, end_date: end_date) || []

    # Format the data for Chart.js
    chart_data = {
      labels: chart_rows.map { |row| Date.parse(row.dimension_values[0].value).strftime("%b %d") },
      datasets: [ {
        label: "Pageviews",
        data: chart_rows.map { |row| row.metric_values[0].value.to_i },
        backgroundColor: "rgba(75, 192, 192, 0.2)",
        borderColor: "rgba(75, 192, 192, 1)",
        borderWidth: 1
      } ]
    }

    # Format the data for the table
    table_data = table_rows.map do |row|
      {
        path: row.dimension_values[0].value,
        pageviews: row.metric_values[0].value,
        users: row.metric_values[1].value
      }
    end

    render json: { chart_data: chart_data, table_data: table_data }, layout: false
  end

  private

  def authorize_dashboard
    authorize! :read, :dashboard
  end

  def set_default_breadcrumb
    helpers.add_breadcrumb "Dashboard", dashboard_root_path
  end

  def user_params
    params.require(:user).permit(:email, :password, :password_confirmation, :role)
  end

  def set_user
    @user = User.find(params[:id])
  end
end
