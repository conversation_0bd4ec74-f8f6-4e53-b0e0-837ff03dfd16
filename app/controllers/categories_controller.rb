class CategoriesController < ApplicationController
  before_action :authorize_categories, only: [ :dashboard_list, :create, :update, :destroy ]
  before_action :authenticate_user!, except: [ :index, :show ]
  before_action :set_category, only: %i[ show edit update destroy ]
  before_action :set_categories_breadcrumbs, except: [ :index, :show ]
  layout "dashboard", except: [ :index, :show ]

  def dashboard_list
    authorize! :read, Category
    set_meta_tags title: t("categories.index.heading")
    @categories = Category.all
  end

  # GET /categories or /categories.json
  def index
    @categories = Category.all
  end

  # GET /categories/1 or /categories/1.json
  def show
  end

  # GET /categories/new
  def new
    @category = Category.new
  end

  # GET /categories/1/edit
  def edit
  end

  # POST /categories or /categories.json
  def create
    @category = Category.new(category_params)

    respond_to do |format|
      if @category.save
        format.html { redirect_to @category, notice: "Category was successfully created." }
        format.json { render :show, status: :created, location: @category }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @category.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /categories/1 or /categories/1.json
  def update
    respond_to do |format|
      if @category.update(category_params)
        format.html { redirect_to @category, notice: "Category was successfully updated.", status: :see_other }
        format.json { render :show, status: :ok, location: @category }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @category.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /categories/1 or /categories/1.json
  def destroy
    @category.destroy!

    respond_to do |format|
      format.html { redirect_to categories_path, notice: "Category was successfully destroyed.", status: :see_other }
      format.json { head :no_content }
    end
  end

  private
    def authorize_categories
      authorize! :read, :category
    end

    # Use callbacks to share common setup or constraints between actions.
    def set_category
      @category = Category.friendly.find(params[:slug])
    end

    # Only allow a list of trusted parameters through.
    def category_params
      params.fetch(:category, {})
    end

    def set_categories_breadcrumbs
      helpers.add_breadcrumb "Dashboard", dashboard_root_path
      if action_name == "dashboard_list"
        helpers.add_breadcrumb t("categories.index.heading"), nil
      else
        helpers.add_breadcrumb t("categories.index.heading"), dashboard_categories_path(locale: I18n.locale)
      end

      case action_name
      when "new", "create"
        helpers.add_breadcrumb t("categories.new.heading", model: Category.model_name.human), nil
      when "edit", "update"
        helpers.add_breadcrumb @post.title, nil if @post
      end
    end
end
