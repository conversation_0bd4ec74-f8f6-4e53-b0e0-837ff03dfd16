class UploadsController < ApplicationController
  def create
    unless params[:file].present?
      render json: { error: "No file" }, status: :unprocessable_entity and return
    end

    file = params[:file]
    blob = ActiveStorage::Blob.create_and_upload!(
      io: file.tempfile, # or file.open
      filename: file.original_filename,
      content_type: file.content_type
    )

    # url_for will generate the URL to the blob (ensure default_url_options[:host] is set in production)
    render json: { url: url_for(blob) }
  end
end
