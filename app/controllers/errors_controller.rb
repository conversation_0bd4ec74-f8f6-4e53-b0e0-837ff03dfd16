class ErrorsController < ApplicationController
  layout "error"
  def access_denied
    @message = params[:message] || I18n.t("access_denied.message")
    @code = params[:code] || 403
    respond_to do |format|
      format.html { render :access_denied, status: :forbidden }
      format.json { render json: { error: @message }, status: :forbidden }
      format.js   { render nothing: true, status: :forbidden }
    end
  end
end
