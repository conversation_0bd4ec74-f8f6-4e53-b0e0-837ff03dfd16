module ApplicationHelper
  include SettingsHelper
  include Pagy::Frontend
  def add_breadcrumb(name, path)
    controller.instance_variable_set(:@breadcrumbs, controller.instance_variable_get(:@breadcrumbs) || [])
    controller.instance_variable_get(:@breadcrumbs) << { name: name, path: path }
  end

  def render_breadcrumbs
    breadcrumbs = controller.instance_variable_get(:@breadcrumbs) || []

    return unless breadcrumbs.any?

    content_tag :div, class: "breadcrumbs text-sm" do
      content_tag :ul do
        breadcrumbs.map.with_index do |crumb, index|
          content_tag :li do
            if crumb[:path] && index < breadcrumbs.length - 1
              link_to crumb[:name], crumb[:path], class: "hover:bg-base-content hover:text-base-100 transition-colors"
            else
              content_tag :span, crumb[:name], class: "text-base-content font-medium"
            end
          end
        end.join.html_safe
      end
    end
  end
end
