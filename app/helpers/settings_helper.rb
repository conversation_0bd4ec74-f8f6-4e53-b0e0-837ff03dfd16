module SettingsHelper
  def get_option(key, default = nil)
    Setting.get(key, default)
  end

  def update_option(key, value)
    Setting.set(key, value)
  end

  def get_user_option(user, key, default = nil)
    UserSetting.get(user, key, default)
  end

  def update_user_option(user, key, value)
    UserSetting.set(user, key, value)
  end

  # Batch load multiple settings - useful for controllers that need many settings
  def get_options(keys_with_defaults = {})
    Setting.get_multiple(keys_with_defaults)
  end

  # Batch load multiple user settings - useful for controllers that need many user settings
  def get_user_options(user, keys_with_defaults = {})
    UserSetting.get_multiple(user, keys_with_defaults)
  end
end
