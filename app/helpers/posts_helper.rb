module <PERSON><PERSON><PERSON><PERSON><PERSON>
  def post_json_ld(post)
    {
      "@context": "https://schema.org",
      "@type": "BlogPosting",
      "headline": post.effective_seo_title,
      "description": post.effective_meta_description,
      "image": post.featured_image.attached? ? url_for(post.featured_image) : nil,
      "datePublished": post.published_at&.iso8601,
      "dateModified": post.updated_at.iso8601,
      "mainEntityOfPage": post_url(post)
    }.compact
  end
end
