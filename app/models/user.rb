class User < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable
  validates :role, inclusion: { in: %w[admin editor viewer author], message: "%{value} is not a valid role" }
  after_initialize :set_default_role, if: :new_record?
  has_one_attached :avatar
  has_many :posts, dependent: :destroy

  validate :avatar_format, if: -> { avatar.attached? }

  has_many :user_settings, dependent: :destroy

  after_create :set_default_settings

  def set_default_role
    self.role ||= "viewer"
  end

  def admin?
    role == "admin"
  end

  def editor?
    role == "editor"
  end

  def viewer?
    role == "viewer"
  end

  def author?
    role == "author"
  end

  def avatar_thumbnail
    if avatar.attached?
      begin
        avatar.variant(resize_to_fill: [ 150, 150 ]).processed
      rescue => e
        Rails.logger.error "Error processing avatar variant: #{e.message}"
        "/default-profile.jpg"
      end
    else
      "/default-profile.jpg"
    end
  end

  private

  def avatar_format
    return unless avatar.attached?

    unless avatar.blob.content_type.start_with?("image/")
      errors.add(:avatar, "must be an image file")
    end

    if avatar.blob.byte_size > 2.megabytes
      errors.add(:avatar, "must be less than 2MB")
    end
  end

  def set_default_settings
    defaults = {
      light_theme: "winter",
      dark_theme: "dark"
    }
    defaults.each do |key, value|
      UserSetting.set(self, key, value)
    end
  end
end
