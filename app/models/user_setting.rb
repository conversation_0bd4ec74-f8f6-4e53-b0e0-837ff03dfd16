class UserSetting < ApplicationRecord
  belongs_to :user
  validates :key, presence: true, uniqueness: { scope: :user_id }

  def self.get(user, key, default = nil)
    return default unless user

    cache_key = "user_setting:#{user.id}:#{key}"
    Rails.cache.fetch(cache_key, expires_in: 1.hour) do
      setting = find_by(user: user, key: key)
      setting&.value || default
    end
  end

  def self.set(user, key, value)
    return nil unless user

    setting = find_or_initialize_by(user: user, key: key)
    setting.value = value
    setting.save!
    # Clear the cache for this specific user setting
    Rails.cache.delete("user_setting:#{user.id}:#{key}")
    value
  end

  # Batch load multiple user settings to reduce database queries
  def self.get_multiple(user, keys_with_defaults = {})
    return keys_with_defaults.transform_values { |default| default } unless user

    cache_keys = keys_with_defaults.keys.map { |key| "user_setting:#{user.id}:#{key}" }
    cached_values = Rails.cache.read_multi(*cache_keys)

    result = {}
    missing_keys = []

    keys_with_defaults.each do |key, default|
      cache_key = "user_setting:#{user.id}:#{key}"
      if cached_values.key?(cache_key)
        result[key] = cached_values[cache_key]
      else
        missing_keys << key
      end
    end

    # Fetch missing settings from database
    unless missing_keys.empty?
      settings = where(user: user, key: missing_keys).pluck(:key, :value).to_h
      missing_keys.each do |key|
        value = settings[key] || keys_with_defaults[key]
        result[key] = value
        # Cache the value
        Rails.cache.write("user_setting:#{user.id}:#{key}", value, expires_in: 1.hour)
      end
    end

    result
  end

  # Clear all user setting caches for a specific user
  def self.clear_user_cache(user)
    return unless user
    Rails.cache.delete_matched("user_setting:#{user.id}:*")
  end
end
