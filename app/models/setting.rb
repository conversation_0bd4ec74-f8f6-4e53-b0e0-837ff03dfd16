class Setting < ApplicationRecord
  validates :key, presence: true, uniqueness: true

  def self.get(key, default = nil)
    Rails.cache.fetch("setting:#{key}", expires_in: 1.hour) do
      setting = find_by(key: key)
      setting&.value || default
    end
  end

  def self.set(key, value)
    setting = find_or_initialize_by(key: key)
    setting.value = value
    setting.save!
    # Clear the cache for this specific setting
    Rails.cache.delete("setting:#{key}")
    value
  end

  # Batch load multiple settings to reduce database queries
  def self.get_multiple(keys_with_defaults = {})
    cache_keys = keys_with_defaults.keys.map { |key| "setting:#{key}" }
    cached_values = Rails.cache.read_multi(*cache_keys)

    result = {}
    missing_keys = []

    keys_with_defaults.each do |key, default|
      cache_key = "setting:#{key}"
      if cached_values.key?(cache_key)
        result[key] = cached_values[cache_key]
      else
        missing_keys << key
      end
    end

    # Fetch missing settings from database
    unless missing_keys.empty?
      settings = where(key: missing_keys).pluck(:key, :value).to_h
      missing_keys.each do |key|
        value = settings[key] || keys_with_defaults[key]
        result[key] = value
        # Cache the value
        Rails.cache.write("setting:#{key}", value, expires_in: 1.hour)
      end
    end

    result
  end

  # Clear all setting caches (useful for bulk updates)
  def self.clear_cache
    Rails.cache.delete_matched("setting:*")
  end
end
