@import "tailwindcss";
@plugin "@tailwindcss/typography";
@plugin "daisyui" {
  themes: all;  
}

@custom-variant dark (&:where([data-theme]:is([data-theme*='dark'], [data-theme*='synthwave'], [data-theme*='hallowen'], [data-theme*='forest'], [data-theme*='black'], [data-theme*='luxury'], [data-theme*='dracula'], [data-theme*='business'], [data-theme*='night'], [data-theme*='coffee'], [data-theme*='dim'], [data-theme*='sunset'], [data-theme*='abyss']), *));

.pagy.nav {
  @apply flex items-center space-x-1 font-semibold text-sm;  
  > a {
    @apply btn btn-outline btn-sm;
  }  
  > a[href] {
    @apply btn-primary;    
  }  
  > a.current {
    @apply btn-active cursor-default;
  }  
  > a[aria-disabled="true"]:not(.current) {
    @apply bg-neutral text-neutral-content opacity-50 cursor-default;
  }
}

.bg-dash{
  background-image: repeating-linear-gradient(to bottom, var(--color-base-100), var(--color-base-100) 15px, var(--color-base-200) 15px, var(--color-base-200) 16px);
}