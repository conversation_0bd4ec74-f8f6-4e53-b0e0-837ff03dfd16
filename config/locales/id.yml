id:  
  home_page:
      index:
        heading: "Website Sekolah"
  error:
    oops: "O<PERSON>, sepertinya ada yang salah."      
    max_desc: "Maksimal 160 karakter"    
    user_not_created: "Pengguna tidak dapat dibuat."    
    user_not_updated: "Pengguna tidak dapat diperbarui."
  notice:
    user_created: "Pengguna berhasil dibuat!"
    user_updated: "Pengguna berhasil diperbarui!"
  access_denied:    
    message: "Anda tidak berwenang mengakses halaman ini"
  sidebar:
    view_site: "Lihat situs" 
  dashboard:
    pageviews: "<PERSON><PERSON><PERSON>" 
    top_pages: "Halaman Teratas"
    refresh: "Segarkan"
    page_path: "<PERSON><PERSON><PERSON>"
  settings:
    title: "Pengaturan"
    site_name: "Nama Situs"
    site_description: "Deskripsi Situs"
    site_light_theme: "Tema Terang Situs"
    site_dark_theme: "Tema Gelap Situs"
    light_theme: "Tema Terang"
    dark_theme: "Tema Gelap"
    user: "Pengaturan Pengguna"
  posts:
    <<: &crud_actions
      list: "Daftar %{model}"
      new:
        heading: "%{model} Baru"
      edit:
        heading: "Edit %{model}"
    index:
      heading: "Postingan"
  users: "Pengguna"
  user: "Pengguna"
  email: "Email"
  role: "Peran"
  created_at: "Dibuat pada"
  actions: "Aksi"
  add: "Tambah"
  show: "Lihat"
  edit: "Edit"
  delete: "Hapus"
  author: "Penulis"
  yes: "Ya"
  no: "Tidak"
  are_you_sure: "Apakah Anda yakin?"
  categories:
    <<: *crud_actions
    index:
      heading: "Kategori"
  name: "Nama"
  slug: "Slug"
  activerecord:
    models:
      post: "Postingan"
      category: "Kategori"
    attributes:
      post:
        title: "Judul"
        excerpt: "Kutipan"
        category: "Kategori"
        published: "Diterbitkan"
        published_at: "Diterbitkan pada"
      category:
        name: "Nama"
        slug: "Slug"
      errors:
        template:
          header:
            one: "1 kesalahan menyebabkan %{model} ini gagal disimpan"
            other: "%{count} kesalahan menyebabkan %{model} ini gagal disimpan"
        models:
          post:
            attributes:
              title:
                blank: "Judul tidak boleh kosong."
              excerpt:
                blank: "Kutipan tidak boleh kosong."
              body:
                blank: "Isi tulisan tidak boleh kosong."
  helpers:
    submit:
      create: "Buat %{model}"
      update: "Perbarui %{model}"
