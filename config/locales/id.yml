id:
  # <PERSON><PERSON><PERSON><PERSON>an umum aplikasi
  common:
    actions:
      add: "Tambah"
      show: "Lihat"
      edit: "Edit"
      delete: "Hapus"
      save: "<PERSON>mpan"
      cancel: "<PERSON><PERSON>"
      back: "<PERSON><PERSON><PERSON>"
      refresh: "Segarkan"
    confirmations:
      are_you_sure: "<PERSON><PERSON><PERSON>h Anda yakin?"
      delete_confirmation: "Apakah Anda yakin ingin menghapus %{item} ini?"
    responses:
      yes: "Ya"
      no: "Tidak"
    status:
      published: "Dipublikasikan"
      draft: "Draf"
      active: "Aktif"
      inactive: "Tidak Aktif"
    fields:
      name: "Nama"
      title: "<PERSON><PERSON><PERSON>"
      slug: "Slug"
      email: "Email"
      role: "<PERSON><PERSON>"
      author: "<PERSON>ulis"
      actions: "Aksi"
      created_at: "Dibuat pada"
      updated_at: "Diperbarui pada"

  # Navigasi dan tata letak
  navigation:
    sidebar:
      view_site: "Lihat situs"

  # Terjemahan khusus halaman
  pages:
    home:
      index:
        heading: "Website Sekolah"
        welcome_message: "Selamat datang di sistem manajemen sekolah kami"

    dashboard:
      index:
        title: "<PERSON><PERSON>"
        pageviews: "<PERSON><PERSON><PERSON>"
        top_pages: "Halaman Teratas"
        page_path: "<PERSON><PERSON><PERSON>"

    settings:
      index:
        title: "Pengaturan"
        site_name: "Nama Situs"
        site_description: "Deskripsi Situs"
        site_light_theme: "Tema Terang Situs"
        site_dark_theme: "Tema Gelap Situs"
        light_theme: "Tema Terang"
        dark_theme: "Tema Gelap"
        user_settings: "Pengaturan Pengguna"

  # Terjemahan khusus sumber daya
  resources:
    posts:
      index:
        heading: "Postingan"
        no_posts: "Tidak ada postingan ditemukan"
      new:
        heading: "Postingan Baru"
      edit:
        heading: "Edit Postingan"
      show:
        title: "Menampilkan Postingan"
      list: "Daftar Postingan"

    categories:
      index:
        heading: "Kategori"
        no_categories: "Tidak ada kategori ditemukan"
      new:
        heading: "Kategori Baru"
      edit:
        heading: "Edit Kategori"
      list: "Daftar Kategori"

    users:
      index:
        heading: "Pengguna"
        no_users: "Tidak ada pengguna ditemukan"
      new:
        heading: "Pengguna Baru"
      edit:
        heading: "Edit Pengguna"
      list: "Daftar Pengguna"

  # Pesan flash
  flash:
    notices:
      user_created: "Pengguna berhasil dibuat!"
      user_updated: "Pengguna berhasil diperbarui!"
      post_created: "Postingan berhasil dibuat!"
      post_updated: "Postingan berhasil diperbarui!"
      category_created: "Kategori berhasil dibuat!"
      category_updated: "Kategori berhasil diperbarui!"
    errors:
      oops: "Oops, sepertinya ada yang salah."
      user_not_created: "Pengguna tidak dapat dibuat."
      user_not_updated: "Pengguna tidak dapat diperbarui."
      post_not_created: "Postingan tidak dapat dibuat."
      post_not_updated: "Postingan tidak dapat diperbarui."
      category_not_created: "Kategori tidak dapat dibuat."
      category_not_updated: "Kategori tidak dapat diperbarui."
    warnings:
      max_desc: "Maksimal 160 karakter"

  # Otorisasi dan kontrol akses
  authorization:
    access_denied:
      message: "Anda tidak berwenang mengakses halaman ini."
      title: "Akses Ditolak"

  # Terjemahan ActiveRecord
  activerecord:
    models:
      post: "Postingan"
      category: "Kategori"
      user: "Pengguna"
    attributes:
      post:
        title: "Judul"
        excerpt: "Ringkasan"
        body: "Konten"
        category: "Kategori"
        published: "Dipublikasikan"
        published_at: "Dipublikasikan pada"
        featured_image: "Gambar Unggulan"
        seo_title: "Judul SEO"
      category:
        name: "Nama"
        slug: "Slug"
        description: "Deskripsi"
      user:
        email: "Email"
        password: "Kata Sandi"
        password_confirmation: "Konfirmasi Kata Sandi"
        role: "Peran"
        avatar: "Avatar"
    errors:
      template:
        header:
          one: "1 kesalahan menyebabkan %{model} ini gagal disimpan"
          other: "%{count} kesalahan menyebabkan %{model} ini gagal disimpan"
      models:
        post:
          attributes:
            title:
              blank: "Judul tidak boleh kosong."
            excerpt:
              blank: "Ringkasan tidak boleh kosong."
            body:
              blank: "Isi tulisan tidak boleh kosong."

  # Helper terjemahan
  helpers:
    submit:
      create: "Buat %{model}"
      update: "Perbarui %{model}"
