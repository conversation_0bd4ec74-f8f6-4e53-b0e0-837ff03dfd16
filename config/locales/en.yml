# Files in the config/locales directory are used for internationalization and
# are automatically loaded by Rails. If you want to use locales other than
# English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t "hello"
#
# In views, this is aliased to just `t`:
#
#     <%= t("hello") %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# To learn more about the API, please read the Rails Internationalization guide
# at https://guides.rubyonrails.org/i18n.html.
#
# Be aware that YAML interprets the following case-insensitive strings as
# booleans: `true`, `false`, `on`, `off`, `yes`, `no`. Therefore, these strings
# must be quoted to be interpreted as strings. For example:
#
#     en:
#       "yes": yup
#       enabled: "ON"

en:
  # Application-wide common translations
  common:
    actions:
      add: "Add"
      show: "Show"
      edit: "Edit"
      delete: "Delete"
      save: "Save"
      cancel: "Cancel"
      back: "Back"
      refresh: "Refresh"
    confirmations:
      are_you_sure: "Are you sure?"
      delete_confirmation: "Are you sure you want to delete this %{item}?"
    responses:
      yes: "Yes"
      no: "No"
    status:
      published: "Published"
      draft: "Draft"
      active: "Active"
      inactive: "Inactive"
    fields:
      name: "Name"
      title: "Title"
      slug: "Slug"
      email: "Email"
      role: "Role"
      author: "Author"
      actions: "Actions"
      created_at: "Created at"
      updated_at: "Updated at"

  # Navigation and layout
  navigation:
    sidebar:
      view_site: "View site"

  # Page-specific translations
  pages:
    home:
      index:
        heading: "Schools Site"
        welcome_message: "Welcome to our school management system"

    dashboard:
      index:
        title: "Dashboard"
        pageviews: "Pageviews"
        top_pages: "Top Pages"
        page_path: "Page Path"

    settings:
      index:
        title: "Settings"
        site_name: "Site Name"
        site_description: "Site Description"
        site_light_theme: "Site Light Theme"
        site_dark_theme: "Site Dark Theme"
        light_theme: "Light Theme"
        dark_theme: "Dark Theme"
        user_settings: "User Settings"

  # Resource-specific translations
  resources:
    posts:
      index:
        heading: "Posts"
        no_posts: "No posts found"
      new:
        heading: "New Post"
      edit:
        heading: "Edit Post"
      show:
        title: "Showing Post"
      list: "List Posts"

    categories:
      index:
        heading: "Categories"
        no_categories: "No categories found"
      new:
        heading: "New Category"
      edit:
        heading: "Edit Category"
      list: "List Categories"

    users:
      index:
        heading: "Users"
        no_users: "No users found"
      new:
        heading: "New User"
      edit:
        heading: "Edit User"
      list: "List Users"

  # Flash messages
  flash:
    notices:
      user_created: "User created successfully!"
      user_updated: "User updated successfully!"
      post_created: "Post created successfully!"
      post_updated: "Post updated successfully!"
      category_created: "Category created successfully!"
      category_updated: "Category updated successfully!"
    errors:
      oops: "Oops, something went wrong."
      user_not_created: "User could not be created."
      user_not_updated: "User could not be updated."
      post_not_created: "Post could not be created."
      post_not_updated: "Post could not be updated."
      category_not_created: "Category could not be created."
      category_not_updated: "Category could not be updated."
    warnings:
      max_desc: "Maximum 160 characters"

  # Authorization and access control
  authorization:
    access_denied:
      message: "You are not authorized to access this page."
      title: "Access Denied"

  # ActiveRecord translations
  activerecord:
    models:
      post: "Post"
      category: "Category"
      user: "User"
    attributes:
      post:
        title: "Title"
        excerpt: "Excerpt"
        body: "Content"
        category: "Category"
        published: "Published"
        published_at: "Published at"
        featured_image: "Featured Image"
        seo_title: "SEO Title"
      category:
        name: "Name"
        slug: "Slug"
        description: "Description"
      user:
        email: "Email"
        password: "Password"
        password_confirmation: "Password Confirmation"
        role: "Role"
        avatar: "Avatar"