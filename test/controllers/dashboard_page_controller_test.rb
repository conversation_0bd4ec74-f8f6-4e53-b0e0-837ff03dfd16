require "test_helper"

class DashboardPageControllerTest < ActionDispatch::IntegrationTest
  test "unauthenticated user should be redirected to login" do
    get dashboard_root_path(locale: :en)
    assert_response :redirect
    # The redirect should be to the login page with locale
    assert_redirected_to new_user_session_path(locale: :en)
  end

  test "access denied page should render correctly" do
    get access_denied_path(locale: :en)
    assert_response :forbidden
    assert_select "h1", "Access Denied"
  end
end
