require "test_helper"

class PostsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @post = posts(:one)
    @admin_user = users(:admin)
  end

  test "should get index" do
    get posts_url(locale: :en)
    assert_response :success
  end

  test "should get new" do
    sign_in @admin_user
    get new_dashboard_post_url(locale: :en)
    assert_response :success
  end

  test "should create post" do
    sign_in @admin_user
    assert_difference("Post.count") do
      post dashboard_posts_url(locale: :en), params: { post: { body: @post.body, excerpt: @post.excerpt, meta_description: @post.meta_description, published: @post.published, published_at: @post.published_at, seo_title: @post.seo_title, slug: "new-unique-slug", title: "New Unique Post Title" } }
    end

    assert_redirected_to post_url(slug: Post.last.slug, locale: :en)
  end

  test "should show post" do
    get post_url(slug: @post.slug, locale: :en)
    assert_response :success
  end

  test "should get edit" do
    sign_in @admin_user
    get edit_dashboard_post_url(slug: @post.slug, locale: :en)
    assert_response :success
  end

  test "should update post" do
    sign_in @admin_user
    patch dashboard_post_url(slug: @post.slug, locale: :en), params: { post: { body: @post.body, excerpt: @post.excerpt, meta_description: @post.meta_description, published: @post.published, published_at: @post.published_at, seo_title: @post.seo_title, slug: @post.slug, title: @post.title } }
    assert_redirected_to post_url(slug: @post.slug, locale: :en)
  end

  test "should destroy post" do
    sign_in @admin_user
    assert_difference("Post.count", -1) do
      delete dashboard_post_url(slug: @post.slug, locale: :en)
    end

    assert_redirected_to posts_url(locale: :en)
  end
end
