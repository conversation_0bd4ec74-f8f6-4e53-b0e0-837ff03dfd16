require "test_helper"

class ErrorsControllerTest < ActionDispatch::IntegrationTest
  test "should get access_denied" do
    get access_denied_path(locale: :en)
    assert_response :forbidden
    assert_select "h1", "Access Denied"
  end

  test "should get access_denied with custom message" do
    get access_denied_path(locale: :en, message: "Custom error message")
    assert_response :forbidden
    assert_select "p", text: "Custom error message"
  end
end
