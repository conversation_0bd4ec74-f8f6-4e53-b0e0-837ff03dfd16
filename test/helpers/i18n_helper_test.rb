# frozen_string_literal: true

require "test_helper"

class I18nHelperTest < ActiveSupport::TestCase
  include I18nHelper

  def setup
    @original_locale = I18n.locale
    I18n.locale = :en
  end

  def teardown
    I18n.locale = @original_locale if @original_locale
  end

  test "translate_action returns correct translation" do
    I18n.locale = :en
    assert_equal "Add", translate_action(:add)
    assert_equal "Edit", translate_action(:edit)
    assert_equal "Delete", translate_action(:delete)

    I18n.locale = :id
    assert_equal "Tambah", translate_action(:add)
    assert_equal "Edit", translate_action(:edit)
    assert_equal "Hapus", translate_action(:delete)
  end

  test "translate_status returns correct translation" do
    I18n.locale = :en
    assert_equal "Published", translate_status(:published)
    assert_equal "Draft", translate_status(:draft)

    I18n.locale = :id
    assert_equal "Dipublikasikan", translate_status(:published)
    assert_equal "Draf", translate_status(:draft)
  end

  test "translate_field returns correct translation" do
    I18n.locale = :en
    assert_equal "Name", translate_field(:name)
    assert_equal "Email", translate_field(:email)

    I18n.locale = :id
    assert_equal "Nama", translate_field(:name)
    assert_equal "Email", translate_field(:email)
  end

  test "translate_confirmation returns correct translation" do
    I18n.locale = :en
    assert_equal "Are you sure?", translate_confirmation
    assert_equal "Are you sure you want to delete this post?", translate_confirmation(:delete, item: "post")

    I18n.locale = :id
    assert_equal "Apakah Anda yakin?", translate_confirmation
    assert_equal "Apakah Anda yakin ingin menghapus post ini?", translate_confirmation(:delete, item: "post")
  end

  test "translate_resource_heading returns correct translation" do
    I18n.locale = :en
    assert_equal "Posts", translate_resource_heading(:post, :index)
    assert_equal "New Post", translate_resource_heading(:post, :new)

    I18n.locale = :id
    assert_equal "Postingan", translate_resource_heading(:post, :index)
    assert_equal "Postingan Baru", translate_resource_heading(:post, :new)
  end

  test "translate_flash_notice returns correct translation" do
    I18n.locale = :en
    assert_equal "User created successfully!", translate_flash_notice(:created, :user)

    I18n.locale = :id
    assert_equal "Pengguna berhasil dibuat!", translate_flash_notice(:created, :user)
  end

  test "translate_flash_error returns correct translation" do
    I18n.locale = :en
    assert_equal "User could not be created.", translate_flash_error(:not_created, :user)

    I18n.locale = :id
    assert_equal "Pengguna tidak dapat dibuat.", translate_flash_error(:not_created, :user)
  end

  test "translate_boolean returns correct translation" do
    I18n.locale = :en
    assert_equal "Yes", translate_boolean(true)
    assert_equal "No", translate_boolean(false)

    I18n.locale = :id
    assert_equal "Ya", translate_boolean(true)
    assert_equal "Tidak", translate_boolean(false)
  end

  test "translate_model returns correct translation" do
    # Create a mock model class for testing
    mock_model = Class.new do
      def self.model_name
        OpenStruct.new(i18n_key: :post)
      end
    end

    I18n.locale = :en
    assert_equal "Post", translate_model(mock_model)

    I18n.locale = :id
    assert_equal "Postingan", translate_model(mock_model)
  end

  test "translate_attribute returns correct translation" do
    # Create a mock model class for testing
    mock_model = Class.new do
      def self.model_name
        OpenStruct.new(i18n_key: :post)
      end
    end

    I18n.locale = :en
    assert_equal "Title", translate_attribute(mock_model, :title)

    I18n.locale = :id
    assert_equal "Judul", translate_attribute(mock_model, :title)
  end

  test "available_locales_with_names returns correct hash" do
    expected = { en: "English", id: "Bahasa Indonesia" }
    assert_equal expected, available_locales_with_names
  end

  test "current_locale_name returns correct name" do
    I18n.locale = :en
    assert_equal "English", current_locale_name

    I18n.locale = :id
    assert_equal "Bahasa Indonesia", current_locale_name
  end

  test "translation_exists? returns correct boolean" do
    assert translate_exists?("common.actions.add", locale: :en)
    assert translate_exists?("common.actions.add", locale: :id)
    assert_not translate_exists?("nonexistent.key", locale: :en)
  end

  test "safe_translate handles missing translations gracefully" do
    result = safe_translate("nonexistent.key")
    assert_equal "Nonexistent key", result
  end

  test "localize_datetime formats dates correctly" do
    date = Time.zone.parse("2024-01-15 10:30:00")

    I18n.locale = :en
    result = localize_datetime(date)
    assert_includes result, "2024"

    # Test with nil
    assert_equal "", localize_datetime(nil)
  end

  test "localize_date formats dates correctly" do
    date = Date.parse("2024-01-15")

    I18n.locale = :en
    result = localize_date(date)
    assert_includes result, "2024"

    # Test with nil
    assert_equal "", localize_date(nil)
  end
end
