# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

admin:
  email: <EMAIL>
  encrypted_password: <%= Devise::Encryptor.digest(User, 'password') %>
  role: admin

editor:
  email: <EMAIL>
  encrypted_password: <%= Devise::Encryptor.digest(User, 'password') %>
  role: editor

viewer:
  email: <EMAIL>
  encrypted_password: <%= Devise::Encryptor.digest(User, 'password') %>
  role: viewer
