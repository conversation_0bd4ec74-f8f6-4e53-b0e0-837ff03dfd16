# frozen_string_literal: true

class I18nValidator
  attr_reader :errors, :warnings

  def initialize
    @errors = []
    @warnings = []
  end

  # Main validation method
  def validate!
    @errors.clear
    @warnings.clear

    validate_locale_completeness
    validate_key_consistency
    validate_interpolation_consistency
    validate_pluralization_rules
    validate_missing_translations

    {
      valid: @errors.empty?,
      errors: @errors,
      warnings: @warnings,
      summary: generate_summary
    }
  end

  private

  # Check if all locales have the same keys
  def validate_locale_completeness
    available_locales = I18n.available_locales
    return if available_locales.length < 2

    base_locale = available_locales.first
    base_keys = extract_all_keys(base_locale)

    available_locales[1..-1].each do |locale|
      locale_keys = extract_all_keys(locale)

      missing_keys = base_keys - locale_keys
      extra_keys = locale_keys - base_keys

      missing_keys.each do |key|
        @errors << "Missing translation key '#{key}' in locale '#{locale}'"
      end

      extra_keys.each do |key|
        @warnings << "Extra translation key '#{key}' in locale '#{locale}' not found in base locale '#{base_locale}'"
      end
    end
  end

  # Check for consistent key structure across locales
  def validate_key_consistency
    I18n.available_locales.each do |locale|
      keys = extract_all_keys(locale)

      keys.each do |key|
        begin
          translation = I18n.t(key, locale: locale, raise: true)

          # Check for empty translations
          if translation.blank?
            @warnings << "Empty translation for key '#{key}' in locale '#{locale}'"
          end

          # Check for untranslated strings (still in English for non-English locales)
          if locale != :en && translation.is_a?(String) && looks_like_english?(translation)
            @warnings << "Possibly untranslated string for key '#{key}' in locale '#{locale}': '#{translation}'"
          end

        rescue I18n::MissingTranslationData
          @errors << "Missing translation for key '#{key}' in locale '#{locale}'"
        end
      end
    end
  end

  # Check interpolation variables consistency
  def validate_interpolation_consistency
    I18n.available_locales.each do |base_locale|
      I18n.available_locales.each do |compare_locale|
        next if base_locale == compare_locale

        extract_all_keys(base_locale).each do |key|
          begin
            base_translation = I18n.t(key, locale: base_locale, raise: true)
            compare_translation = I18n.t(key, locale: compare_locale, raise: true)

            next unless base_translation.is_a?(String) && compare_translation.is_a?(String)

            base_vars = extract_interpolation_variables(base_translation)
            compare_vars = extract_interpolation_variables(compare_translation)

            if base_vars != compare_vars
              @errors << "Interpolation variables mismatch for key '#{key}' between '#{base_locale}' #{base_vars.inspect} and '#{compare_locale}' #{compare_vars.inspect}"
            end

          rescue I18n::MissingTranslationData
            # Skip missing translations, they're handled elsewhere
          end
        end
      end
    end
  end

  # Check pluralization rules
  def validate_pluralization_rules
    I18n.available_locales.each do |locale|
      extract_all_keys(locale).each do |key|
        begin
          translation = I18n.t(key, locale: locale, raise: true)

          if translation.is_a?(Hash) && (translation.key?(:one) || translation.key?(:other))
            # This is a pluralization hash
            required_keys = I18n.backend.send(:pluralization_keys, locale)

            required_keys.each do |plural_key|
              unless translation.key?(plural_key)
                @warnings << "Missing pluralization key '#{plural_key}' for '#{key}' in locale '#{locale}'"
              end
            end
          end

        rescue I18n::MissingTranslationData
          # Skip missing translations
        end
      end
    end
  end

  # Check for missing translations in views and controllers
  def validate_missing_translations
    # This would require parsing view files and controllers
    # For now, we'll just check if commonly used keys exist
    common_keys = [
      "common.actions.add",
      "common.actions.edit",
      "common.actions.delete"
    ]

    I18n.available_locales.each do |locale|
      common_keys.each do |key|
        unless I18n.exists?(key, locale)
          @errors << "Common key '#{key}' missing in locale '#{locale}'"
        end
      end
    end
  end

  # Extract all translation keys for a locale
  def extract_all_keys(locale, prefix = "", hash = nil)
    # Load translations if not already loaded
    I18n.backend.load_translations unless I18n.backend.initialized?

    hash ||= I18n.backend.send(:translations)[locale] || {}
    keys = []

    hash.each do |key, value|
      current_key = prefix.empty? ? key.to_s : "#{prefix}.#{key}"

      if value.is_a?(Hash) && !is_pluralization_hash?(value)
        keys.concat(extract_all_keys(locale, current_key, value))
      else
        keys << current_key
      end
    end

    keys
  end

  # Check if a hash represents pluralization rules
  def is_pluralization_hash?(hash)
    return false unless hash.is_a?(Hash)
    pluralization_keys = [ :zero, :one, :two, :few, :many, :other ]
    hash.keys.any? { |key| pluralization_keys.include?(key.to_s.to_sym) }
  end

  # Extract interpolation variables from a string
  def extract_interpolation_variables(string)
    string.scan(/%\{([^}]+)\}/).flatten.sort
  end

  # Simple heuristic to detect if a string looks like English
  def looks_like_english?(string)
    # Check for common English words and patterns
    english_indicators = [
      /\b(the|and|or|but|in|on|at|to|for|of|with|by)\b/i,
      /\b(is|are|was|were|have|has|had|will|would|could|should)\b/i,
      /\b(this|that|these|those|here|there|where|when|what|who|how|why)\b/i
    ]

    english_indicators.any? { |pattern| string.match?(pattern) }
  end

  # Generate a summary of the validation results
  def generate_summary
    {
      total_errors: @errors.length,
      total_warnings: @warnings.length,
      locales_checked: I18n.available_locales.length,
      status: @errors.empty? ? "PASS" : "FAIL"
    }
  end

  # Class methods for easy access
  class << self
    def validate!
      new.validate!
    end

    def check_key_exists(key, locale = I18n.locale)
      I18n.exists?(key, locale)
    end

    def find_missing_keys(base_locale = :en)
      validator = new
      missing_keys = {}

      I18n.available_locales.each do |locale|
        next if locale == base_locale

        base_keys = validator.send(:extract_all_keys, base_locale)
        locale_keys = validator.send(:extract_all_keys, locale)
        missing_keys[locale] = base_keys - locale_keys
      end

      missing_keys
    end

    def find_unused_keys
      # This would require scanning the codebase for translation usage
      # For now, return empty array
      []
    end

    def translation_coverage(locale = I18n.locale)
      validator = new
      all_keys = validator.send(:extract_all_keys, locale)

      translated_count = all_keys.count do |key|
        begin
          translation = I18n.t(key, locale: locale, raise: true)
          !translation.blank?
        rescue I18n::MissingTranslationData, ArgumentError
          false
        end
      end

      {
        total_keys: all_keys.length,
        translated_keys: translated_count,
        coverage_percentage: all_keys.length > 0 ? (translated_count.to_f / all_keys.length * 100).round(2) : 0
      }
    end
  end
end
